<template>
  <div class="middleCon">
    <div class="mainView" id="mainView">
      <div
        v-show="!commonStore.is110 && commonStore.showSvg"
        id="svg_panel"
        style="position: absolute"
      ></div>
      <div
        v-show="commonStore.is110 && commonStore.showSvg"
        id="svg_panel2"
        style="position: absolute"
      ></div>
    </div>
  </div>
</template>

<script setup>
import { useTopoStore, useCommonStore } from "@/stores/";
import { onBeforeUnmount, onMounted, ref } from "vue";
// import { useWebsocket } from "@/hooks/useWebsocket.js";
import {
  initSvg,
  initCon,
  initData,
  freshData,
  cancelRefreshData,
  bindZoom,
  bindZoom2,
} from "@/utils/draw/";

const topoStore = useTopoStore();
const commonStore = useCommonStore();

const loadSvg = async () => {
  await topoStore.getMiniSvgData();
  const { mapId } = topoStore.miniSvgData.map;
  // await topoStore.getHotspotList(mapId);

  commonStore.showSvg = true;

  initCon();

  bindZoom();
  bindZoom2();
  initData(mapId);
  freshData(mapId);
};

onMounted(async () => {
  await initSvg();
  await loadSvg();
  //   useWebsocket(import.meta.env.VITE_WS_HOST);
});

onBeforeUnmount(() => {
  cancelRefreshData();
});
</script>

<style></style>
