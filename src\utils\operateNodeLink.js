import * as d3 from "d3";
import { useTopoStore } from "@/stores/";
import { locatePosition } from "@/utils/draw/modules/event.js";
import { useCommonStore } from "@/stores/";

// 生成路径点的函数
function generatePathPoint(d) {
  let points = [];
  if (d) {
    const arr = d.split(" ").map((ele) => (isNaN(+ele) ? ele : +ele));
    for (let i = 0; i < arr.length; i++) {
      if (arr[i] === "M" || arr[i] === "L") {
        points.push({
          x: arr[i + 1],
          y: arr[i + 2],
          type: arr[i],
        });
      } else if (arr[i] === "Q") {
        points.push({
          x: arr[i + 1],
          y: arr[i + 2],
          q: {
            x: arr[i + 3],
            y: arr[i + 4],
          },
        });
      } else if (arr[i] === "C") {
        points.push({
          x: arr[i + 1],
          y: arr[i + 2],
          c: [
            {
              x: arr[i + 3],
              y: arr[i + 4],
            },
            {
              x: arr[i + 5],
              y: arr[i + 6],
            },
          ],
        });
      } else if (arr[i] === "A") {
        points.push({
          x: arr[i + 1],
          y: arr[i + 2],
          a: {
            rx: arr[i + 3],
            ry: arr[i + 4],
            rot: arr[i + 5],
            laf: arr[i + 6],
            sf: arr[i + 7],
          },
        });
      }
    }

    return points;
  }
}

let timer = null;

// 闪烁节点链接的函数
function flashNodeLink(ele) {
  // 清除当前可能存在的定时器，确保只保留最后一次点击的效果
  if (timer) {
    clearTimeout(timer);
  }

  // 移除所有元素的“topo-flash”类，停止之前的任何闪烁效果
  d3.selectAll(".topo-flash").classed("topo-flash", false);

  // 使用Promise链保证清除类名操作先于设置新类名和定时器执行
  Promise.resolve()
    .then(() => {
      // 根据ele的类型选择要闪烁的元素并添加类名
      let selector;
      if (ele.linkId) {
        selector = `#topoLink_${ele.linkId.replace("#", "_")}`;
      } else {
        selector = `#node_${ele.nodeId.replace("#", "_")}`;
      }
      d3.select(selector).classed("topo-flash", true);

      // 设置新的定时器，用于在指定时间后移除“topo-flash”类
      return new Promise((resolve) => {
        timer = setTimeout(() => {
          d3.select(selector).classed("topo-flash", false);
          resolve(); // 定时器执行完毕后resolve promise
        }, 8000);
      });
    })
    .catch((error) => {
      console.error("An error occurred:", error);
    });
}

// 闪烁节点链接列表的函数
export function flashNodeLinkList(nodeLinkList) {
  // 清除当前可能存在的定时器，确保只保留最后一次点击的效果
  if (timer) {
    clearTimeout(timer);
  }

  // 移除所有元素的“topo-flash”类，停止之前的任何闪烁效果
  d3.selectAll(".topo-flash").classed("topo-flash", false);

  // 使用Promise链保证清除类名操作先于设置新类名和定时器执行
  Promise.resolve()
    .then(() => {
      // 根据ele的类型选择要闪烁的元素并添加类名
      let selector;
      nodeLinkList.forEach((ele) => {
        if (ele.linkId) {
          selector = `#topoLink_${ele.linkId.replace("#", "_")}`;
        } else {
          selector = `#node_${ele.nodeId.replace("#", "_")}`;
        }
        d3.select(selector).classed("topo-flash", true);
      });

      // 设置新的定时器，用于在指定时间后移除“topo-flash”类
      return new Promise((resolve) => {
        timer = setTimeout(() => {
          d3.selectAll(".topo-flash").classed("topo-flash", false);
          resolve(); // 定时器执行完毕后resolve promise
        }, 8000);
      });
    })
    .catch((error) => {
      console.error("An error occurred:", error);
    });
}

// 移动位置的函数
function movePosition(ele, isFlsh = true) {
  let width = document.getElementById("mainView").offsetWidth;
  let height = document.getElementById("mainView").offsetHeight;

  let pos = {};

  if (ele.linkId) {
    let points = generatePathPoint(ele.linkPath);

    let centerX = 0;
    let centerY = 0;

    for (let i = 0; i < points.length; i++) {
      centerX += Number(points[i].x);
      centerY += Number(points[i].y);
    }

    let center = {
      x: centerX / points.length,
      y: centerY / points.length,
    };

    pos = {
      x: -center.x + width / 2,
      y: -center.y + height / 2,
      k: 10,
    };
  } else {
    pos = {
      x: -Number(ele.nodePosition.split(",")[0]) + width / 2,
      y: -Number(ele.nodePosition.split(",")[1]) + height / 2,
      k: 10,
    };
  }
  isFlsh && flashNodeLink(ele);

  return pos;

  // topoComp.flyToSub(pos);
  // topoComp.drawEnd = function () {
  //   setTimeout(() => {
  //     topoComp.drawEnd = null;
  //     if (ele.linkId) {
  //       console.log(`#topoLink_${ele.linkId.replace("#", "_")}`);

  //       d3.select(`#topoLink_${ele.linkId.replace("#", "_")}`).classed("topo-flash", true);

  //       setTimeout(() => {
  //         d3.select(`#topoLink_${ele.linkId.replace("#", "_")}`).classed("topo-flash", false);
  //       }, 8000);
  //     } else {
  //       console.log(`#node_${ele.nodeId.replace("#", "_")}`);
  //       d3.select(`#node_${ele.nodeId.replace("#", "_")}`).classed("topo-flash", true);

  //       setTimeout(() => {
  //         d3.select(`#node_${ele.nodeId.replace("#", "_")}`).classed("topo-flash", false);
  //       }, 8000);
  //     }
  //   });
  // };
}

// 获取元素ID的函数
const getElementId = (id) => {
  if (!id) return;
  let rtKeyId = "";
  if (id.slice(0, 1) === "0") {
    rtKeyId = id.slice(1, 4) + ":" + id.slice(4) + "0000";
  } else {
    rtKeyId = id.slice(0, 4) + ":" + id.slice(4) + "0000";
  }

  return rtKeyId;
};

// 设置浮动位置的函数
const setFloatPosition = (item, textList) => {
  if (item.linkId) {
    textList.forEach((text) => {
      if (text.bindLink === item.linkId) {
        // 获取有功值的位置
        const [x, y] = text.nodePosition.split(",").map((d) => Number(d) / 10);
        const [w] = text.nodeSize.split("*").map((d) => Number(d) / 10);
        item.floatX = x + w;
        item.floatY = y;
      }
    });
  } else {
    const [x, y] = item.nodePosition.split(",").map((d) => Number(d) / 10);
    const [w] = item.nodeSize.split("*").map((d) => Number(d) / 10);
    item.floatX = x + w;
    item.floatY = y;
  }
  return item;
};

let textEl;

// 获取文本宽度的函数
function getTextWidth(text, size) {
  if (!textEl) {
    textEl = document.getElementById("textWidth");
  }

  textEl.style.fontSize = `${size}px`;

  textEl.textContent = text;
  return {
    w: textEl.clientWidth,
    h: textEl.clientHeight,
  };
}

/**
 * 根据检修数据的stationId获取对应的node和link信息
 * @param {Array} nodeLinkList
 * @param {Array} repairList
 */
const getRepairData = (nodeLinkList, repairList, type) => {
  const list = [];
  const textList = nodeLinkList.filter((item) => item.nodeType === "text");

  nodeLinkList.forEach((item) => {
    repairList.forEach((ele) => {
      if (ele.stationIdList) {
        ele.stationIdList.forEach((stationId) => {
          const rtKeyId = getElementId(stationId);
          if (!rtKeyId) return;
          if (item.metaData && item.metaData.rtKeyId0 === rtKeyId) {
            // 获取浮窗弹出的位置
            item = setFloatPosition(item, textList);
            item.repairData = ele;
            if (
              item.floatX !== null &&
              item.floatX !== undefined &&
              item.floatY !== null &&
              item.floatY !== undefined
            ) {
              if (type === "tooltip") {
                const deviceNameSize = getTextWidth(ele.stationName || ele.deviceName, 14);
                const dateSize = getTextWidth(ele.date, 14);
                item.tooltipSize = {
                  w: Math.max(deviceNameSize.w, dateSize.w) + 15,
                  //   10是距离上面的高度
                  h: deviceNameSize.h + dateSize.h + 10 + 20,
                };
              }
              list.push(item);
            }
          }
        });
      } else {
        const rtKeyId = getElementId(ele.stationId);
        if (!rtKeyId) return;

        if (item.metaData && item.metaData.rtKeyId0 === rtKeyId) {
          // 获取浮窗弹出的位置
          item = setFloatPosition(item, textList);
          item.repairData = ele;
          if (
            item.floatX !== null &&
            item.floatX !== undefined &&
            item.floatY !== null &&
            item.floatY !== undefined
          ) {
            if (type === "tooltip") {
              const deviceNameSize = getTextWidth(ele.stationName || ele.deviceName, 14);
              const dateSize = getTextWidth(ele.date, 14);
              item.tooltipSize = {
                w: Math.max(deviceNameSize.w, dateSize.w) + 15,
                //   10是距离上面的高度
                h: deviceNameSize.h + dateSize.h + 10 + 20,
              };
            }
            list.push(item);
          }
        }
      }
    });
  });
  return list;
};

// 获取实时键ID的函数
const getRtKeyId = (dom, nodeLinkList) => {
  if (!dom.id) return;
  const id = dom.id
    .split("_")
    .slice(1)
    .map((item, index) => {
      if (index === 0) return item;
      if (index === 1) {
        return "#" + item;
      } else {
        return "_" + item;
      }
    })
    .join("");
  const item = nodeLinkList.find((item) => (item.nodeId || item.linkId) === id);
  if (!item || !item.metaData.rtKeyId0) return;
  //   const stationId = geStationId(item.metaData.rtKeyId0);
  return item.metaData.rtKeyId0;
};

// 获取边缘位置的函数
const getEdgePosition = (ele) => {
  let position = {};
  if (ele.linkId) {
    if (!ele.linkPath) return position;
    const points = generatePathPoint(ele.linkPath);

    const xList = points.map((ele) => ele.x).sort((a, b) => a - b);
    const yList = points.map((ele) => ele.y).sort((a, b) => a - b);
    const l = xList[0];
    const r = xList[xList.length - 1];
    const t = yList[0];
    const b = yList[yList.length - 1];
    position = {
      l,
      r,
      t,
      b,
    };
  } else {
    let [x, y] = ele.nodePosition.split(",").map((d) => Number(d));
    let [w, h] = ele.nodeSize.split("*").map((d) => Number(d));

    position = {
      l: x,
      r: x + w,
      t: y,
      b: y + h,
    };
  }
  return position;
};

// 判断点是否在矩形内的函数
function isPointInsideRectangle(x, y, left, top, right, bottom) {
  return x >= left && x <= right && y >= top && y <= bottom;
}

const sublayerMap = {
  SuzhouNorth: "ShwSOAZoB89",
  SuzhouSouth: "SoppSdC3Apo",
  SuXiChang: "S8b9uxJ9AUo",
  NanjingWest: "S8b9uxJ9AUo",
};

/**
 * 根据热区范围获取热区内的元素
 * @param {*} nodeLinkList
 * @param {*} scopeInfo
 * @returns
 */
const getDataByScope = (nodeLinkList, scopeInfo, name) => {
  const sublayerId = sublayerMap[name];
  const links = [];
  const nodes = [];
  //   const { w, h } = scopeInfo.size;
  //   const scopeL = Math.abs(scopeInfo.offset.x);
  //   const scopeT = Math.abs(scopeInfo.offset.y);

  //   const scopeR = scopeL + w;
  //   const scopeB = scopeT + h;
  nodeLinkList.forEach((item) => {
    if (item.sublayerList.some((sublayer) => sublayer.sublayerId === sublayerId)) {
      item.linkId ? links.push(item) : nodes.push(item);
    }
    // 500KV网架根据热区去筛选
    // if (item.sublayerList.some((sublayer) => sublayer.sublayerId === "SDAfqbRLOiv")) {
    //   const { l, r, t, b } = getEdgePosition(item);
    //   if (item.linkId) {
    //     // 如果是线，只要线的一部分在范围内就可以
    //     if (
    //       isPointInsideRectangle(l, t, scopeL, scopeT, scopeR, scopeB) ||
    //       isPointInsideRectangle(r, b, scopeL, scopeT, scopeR, scopeB) ||
    //       isPointInsideRectangle(l, b, scopeL, scopeT, scopeR, scopeB) ||
    //       isPointInsideRectangle(r, t, scopeL, scopeT, scopeR, scopeB)
    //     ) {
    //       links.push(item);
    //     }
    //   } else {
    //   if (l >= scopeL && r <= scopeR && t >= scopeT && b <= scopeB) {
    //     nodes.push(item);
    //   }
    //   }
    // } else {
    //   // 下钻信息跟据子图层去筛选
    //   if (item.sublayerList.some((sublayer) => sublayer.sublayerId === sublayerId)) {
    //     item.linkId ? links.push(item) : nodes.push(item);
    //   }
    // }
  });
  return { links, nodes };
};

// 点击项的处理函数
const onItemCLick = (data) => {
  const commonStore = useCommonStore();
  let stationIdList = data.stationIdList || [data.stationId];
  if (!stationIdList.length) return;
  const keys = stationIdList.map((item) => getElementId(item)) || [];
  const topoStore = useTopoStore();

  const nodeLinkList = [];
  let centerX = 0;
  let centerY = 0;

  topoStore.nodeLinkList.forEach((item) => {
    // 判断display是否为none，如果为none 则返回

    if (
      item.nodeId &&
      d3.select(`#node_${item.nodeId.replace("#", "_")}`).style("display") === "none"
    )
      return;
    if (
      item.linkId &&
      d3.select(`#topoLink_${item.linkId.replace("#", "_")}`).style("display") === "none"
    )
      return;

    if (!item.metaData) return;
    if (keys.includes(item.metaData.rtKeyId0) || keys.includes(item.metaData.name)) {
      let pos = movePosition(item, false);
      nodeLinkList.push(item);
      centerX += pos.x;
      centerY += pos.y;
    }
  });
  if (!nodeLinkList.length) return;

  if (nodeLinkList.length > 0) {
    const avgPos = {
      x: centerX / nodeLinkList.length,
      y: centerY / nodeLinkList.length,
      k: 1 / commonStore.svgScale,
    };
    locatePosition(avgPos, nodeLinkList);
  }

  flashNodeLinkList(nodeLinkList);
};

export { movePosition, getElementId, getRepairData, getRtKeyId, getDataByScope, onItemCLick };
