<template>
  <NLayout has-sider sider-placement="right" class="h-full">
    <NLayoutContent>
      <div class="map-layout flex">
        <div class="map-conatiner flex flex-1 flex-col p-3 pt-0 mr-3">
          <MapOperate></MapOperate>
          <MainView></MainView>
        </div>
        <!-- <RepairView></RepairView> -->
      </div>
    </NLayoutContent>
    <RepairView></RepairView>
  </NLayout>

  <ImportantRepair ref="importantRepair"></ImportantRepair>
</template>

<script setup>
import RepairView from "@/components/RepairView/index.vue";
import ImportantRepair from "@/components/ImportantRepair/index.vue";
import MainView from "@/components/MainView/index.vue";
import MapOperate from "@/components/MapOperate/index.vue";
import { NLayout, NLayoutContent } from "naive-ui";
</script>

<style lang="scss">
@use "../../assets/scss/moudle/page.scss";
</style>
