import * as d3 from "d3";
import { initPos } from "./assistant";
import { useCommonStore } from "@/stores/modules/common";
import { get } from "@/http/axios.js";
import Cookies from "js-cookie";

export let zoom220 = null;
export let zoom110 = null;

function _moveFloatGroup() {
  const commonStore = useCommonStore();

  d3.select("#svg_panel .mainSVG")
    .select(".tooltip_group")
    .style(
      "transform",
      `translateX(${commonStore.transform.x}px) translateY(${commonStore.transform.y}px)`,
    );
  d3.select("#svg_panel .mainSVG")
    .selectAll(".tooltip_content")
    .each((d, i, n) => {
      const node = d3.select(n[i]);
      const x = node.attr("data-x");
      const y = node.attr("data-y");
      node.style(
        "transform",
        `translateX(${x * commonStore.transform.k}px) translateY(${y * commonStore.transform.k}px)`,
      );
    });

  d3.select("#svg_panel2 .mainSVG")
    .select(".tooltip_group")
    .style(
      "transform",
      `translateX(${commonStore.transform.x}px) translateY(${commonStore.transform.y}px)`,
    );
  d3.select("#svg_panel2 .mainSVG")
    .selectAll(".tooltip_content")
    .each((d, i, n) => {
      const node = d3.select(n[i]);
      const x = node.attr("data-x");
      const y = node.attr("data-y");
      node.style(
        "transform",
        `translateX(${x * commonStore.transform.k}px) translateY(${y * commonStore.transform.k}px)`,
      );
    });
}

let scaleInit = 1;
export const bindZoom = () => {
  let pos = initPos();
  const commonStore = useCommonStore();

  scaleInit = pos.k * 1.5 + 0.5;
  zoom220 = d3.zoom().scaleExtent([pos.k, 15]).on("zoom", zoomed);

  d3.select("#svg_panel .mainSVG")
    .call(zoom220)
    .on("dblclick.zoom", null)
    .on("dblclick", dblclickFun)
    .on("contextmenu", (e) => {
      e.preventDefault();
      handleContextmenu(e);
    })
    .call(zoom220.transform, d3.zoomIdentity.translate(pos.x, pos.y).scale(pos.k));

  function zoomed(e) {
    let transform = e.transform;
    commonStore.transform = transform;
    _moveFloatGroup();
    d3.select("#svg_panel .nari_current_container").attr("transform", transform);

    if (!commonStore.isChange || !commonStore.is110KV35KVVisible) return;

    if (transform.k >= scaleInit) {
      d3.select("#svg_panel2 .mainSVG").call(
        zoom110.transform,
        d3.zoomIdentity.translate(transform.x, transform.y).scale(transform.k),
      );
      commonStore.is110 = true;
    }
  }
};

export const bindZoom2 = () => {
  const commonStore = useCommonStore();
  let pos = initPos();

  zoom110 = d3.zoom().scaleExtent([1, 15]).on("zoom", zoomed);
  d3.select("#svg_panel2 .mainSVG")
    .call(zoom110)
    .on("dblclick.zoom", null)
    .on("dblclick", dblclickFun)
    .on("contextmenu", (e) => {
      e.preventDefault();
      handleContextmenu(e);
    })
    .call(zoom110.transform, d3.zoomIdentity.translate(pos.x, pos.y).scale(pos.k));

  function zoomed(e) {
    let transform = e.transform;
    commonStore.transform = transform;
    _moveFloatGroup();
    d3.select("#svg_panel2 .nari_current_container").attr("transform", transform);

    if (!commonStore.isChange || !commonStore.is220ViewVisible) return;

    if (transform.k < scaleInit) {
      d3.select("#svg_panel .mainSVG").call(
        zoom220.transform,
        d3.zoomIdentity.translate(transform.x, transform.y).scale(transform.k),
      );
      commonStore.is110 = false;
    }
  }
};

export const maintainSvgPosition = () => {
  const commonStore = useCommonStore();

  // 110kv 35KV 显示
  if (!commonStore.is220ViewVisible && commonStore.is110KV35KVVisible) {
    commonStore.is110 = true;
    d3.select("#svg_panel2 .mainSVG").call(
      zoom110.transform,
      d3.zoomIdentity
        .translate(commonStore.transform.x, commonStore.transform.y)
        .scale(commonStore.transform.k),
    );
  } else {
    commonStore.is110 = false;
    d3.select("#svg_panel .mainSVG").call(
      zoom220.transform,
      d3.zoomIdentity
        .translate(commonStore.transform.x, commonStore.transform.y)
        .scale(commonStore.transform.k),
    );
  }
};

export const resetSvgPosition = () => {
  const commonStore = useCommonStore();
  commonStore.isChange = false;

  let containerId = "svg_panel";

  let _zoom = zoom220;
  // 如果只勾选区域图
  if (!commonStore.is220ViewVisible && commonStore.is110KV35KVVisible) {
    containerId = "svg_panel2";
    commonStore.is110 = true;
    _zoom = zoom110;
  } else {
    containerId = "svg_panel";
    commonStore.is110 = false;
    _zoom = zoom220;
  }

  const pos = initPos();

  d3.select(`#${containerId} .mainSVG`)
    .transition()
    .duration(1000)
    .call(_zoom.transform, d3.zoomIdentity.translate(pos.x, pos.y).scale(pos.k))
    .on("end", () => {
      commonStore.isChange = true;
    });
};

/**
 * 如果110图显示，则跳转110
 * @param {*} pos
 */
export const locatePosition = (pos, nodeLinkList) => {
  const commonStore = useCommonStore();
  const { subLayer110List, subLayer220List, is110KV35KVVisible, is220ViewVisible } = commonStore;

  // let isIn110Sublayer = false;
  let isIn220Sublayer = false;
  nodeLinkList.forEach((element) => {
    if (!element.sublayerList) return;
    const sublayerIdList = element.sublayerList.map((item) => item.sublayerId);
    // if (subLayer110List.some((item) => sublayerIdList.includes(item))) {
    //   isIn110Sublayer = true;
    // }

    if (subLayer220List.some((item) => sublayerIdList.includes(item))) {
      console.log("===");

      isIn220Sublayer = true;
    }
  });

  d3.select("#svg_panel2 .mainSVG").call(
    zoom110.transform,
    d3.zoomIdentity.translate(pos.x, pos.y).scale(pos.k),
  );

  //数据不在220里面 并且 勾选了110 不进行处理

  if (!isIn220Sublayer && !is110KV35KVVisible) return;

  d3.select("#svg_panel .mainSVG").call(
    zoom220.transform,
    d3.zoomIdentity.translate(pos.x, pos.y).scale(pos.k),
  );

  // console.log("🚀 ~ locatePosition ~ isIn110:", isIn110);
  // if (isIn110) {
  //   d3.select("#svg_panel2 .mainSVG").call(
  //     zoom110.transform,
  //     d3.zoomIdentity.translate(pos.x, pos.y).scale(pos.k),
  //   );
  //   if (commonStore.is110ViewVisible) {
  //     d3.select("#svg_panel .mainSVG").call(
  //       zoom220.transform,
  //       d3.zoomIdentity.translate(pos.x, pos.y).scale(pos.k),
  //     );
  //   }
  // } else {
  //   d3.select("#svg_panel .mainSVG").call(
  //     zoom220.transform,
  //     d3.zoomIdentity.translate(pos.x, pos.y).scale(pos.k),
  //   );
  // }
};

export const moveSvgPosition = (isCollapsed) => {
  const commonStore = useCommonStore();

  commonStore.transform.x += isCollapsed ? 300 : -300;
  d3.select("#svg_panel2 .mainSVG").call(
    zoom110.transform,
    d3.zoomIdentity
      .translate(commonStore.transform.x, commonStore.transform.y)
      .scale(commonStore.transform.k),
  );
  d3.select("#svg_panel .mainSVG").call(
    zoom220.transform,
    d3.zoomIdentity
      .translate(commonStore.transform.x, commonStore.transform.y)
      .scale(commonStore.transform.k),
  );
};

const geStationId = (rtKeyId) => {
  if (!rtKeyId) return;
  let stationId = rtKeyId.replace(":", "").slice(0, -4);
  if (stationId.slice(0, 1) !== "0") {
    stationId = 0 + stationId;
  }
  return stationId;
};

/**
 * 站内图双击事件
 */
const dblclickFun = (e) => {
  const target = e.target;
  if (target.getAttribute("id") && target.getAttribute("id").includes("topoLink")) {
    return;
  } else {
    const svg = target.closest("svg"); // 获取中间层节点
    if (svg) {
      const rtKeyId = svg.getAttribute("keyid");
      if (!rtKeyId) {
        window.$message.warning("该站点无数据！");
        return;
      }
      get("/dwyztApp/dwyzt/getStationGraphByRtKeyId", { rtKeyId: geStationId(rtKeyId) }).then(
        (res) => {
          if (res.code === "0000") {
            window.open(res.data);
          }
        },
      );

      // .catch(() => {
      //   window.open(
      //     `http://24.46.6.8:9000/osp/jkz_portal/login.jsp?redirect=${window.location.href}`,
      //   );
      // });
    }
  }
};

const handleContextmenu = (e) => {
  const target = e.target;
  if (target.getAttribute("id") && target.getAttribute("id").includes("topoLink")) {
    return;
  }
  const svg = target.closest("svg");
  console.log("🚀 ~ handleContextmenu ~ svg:", svg);
};
