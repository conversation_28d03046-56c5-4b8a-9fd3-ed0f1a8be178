<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>SVG转PDF工具</title>
    <!-- 引入jsPDF库 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <style>
      body {
        font-family: "Microsoft YaHei", Arial, sans-serif;
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
        background-color: #f5f5f5;
      }

      .container {
        background: white;
        padding: 30px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }

      h1 {
        text-align: center;
        color: #333;
        margin-bottom: 30px;
      }

      .upload-area {
        border: 2px dashed #ddd;
        border-radius: 10px;
        padding: 40px;
        text-align: center;
        margin-bottom: 20px;
        transition: border-color 0.3s;
      }

      .upload-area:hover {
        border-color: #007bff;
      }

      .upload-area.dragover {
        border-color: #007bff;
        background-color: #f8f9fa;
      }

      #fileInput {
        display: none;
      }

      .upload-btn {
        background-color: #007bff;
        color: white;
        padding: 12px 24px;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        font-size: 16px;
        transition: background-color 0.3s;
      }

      .upload-btn:hover {
        background-color: #0056b3;
      }

      .preview-area {
        margin: 20px 0;
        text-align: center;
      }

      .svg-preview {
        max-width: 100%;
        max-height: 400px;
        border: 1px solid #ddd;
        border-radius: 5px;
        margin: 10px 0;
      }

      .convert-btn {
        background-color: #28a745;
        color: white;
        padding: 12px 24px;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        font-size: 16px;
        margin: 10px;
        transition: background-color 0.3s;
      }

      .convert-btn:hover {
        background-color: #218838;
      }

      .convert-btn:disabled {
        background-color: #6c757d;
        cursor: not-allowed;
      }

      .status {
        margin: 20px 0;
        padding: 10px;
        border-radius: 5px;
        text-align: center;
      }

      .status.success {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
      }

      .status.error {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
      }

      .status.info {
        background-color: #d1ecf1;
        color: #0c5460;
        border: 1px solid #bee5eb;
      }

      .hidden {
        display: none;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>SVG转PDF工具</h1>

      <div class="upload-area" id="uploadArea">
        <p>点击选择SVG文件或拖拽文件到此处</p>
        <button class="upload-btn" onclick="document.getElementById('fileInput').click()">
          选择SVG文件
        </button>
        <input type="file" id="fileInput" accept=".svg" />
      </div>

      <div class="preview-area hidden" id="previewArea">
        <h3>SVG预览：</h3>
        <div id="svgContainer"></div>
        <button class="convert-btn" id="convertBtn" onclick="convertToPDF()">
          转换为PDF并下载
        </button>
      </div>

      <div class="status hidden" id="statusDiv"></div>
    </div>

    <script>
      let svgContent = "";
      let fileName = "";

      // 获取DOM元素
      const fileInput = document.getElementById("fileInput");
      const uploadArea = document.getElementById("uploadArea");
      const previewArea = document.getElementById("previewArea");
      const svgContainer = document.getElementById("svgContainer");
      const convertBtn = document.getElementById("convertBtn");
      const statusDiv = document.getElementById("statusDiv");

      // 文件选择事件
      fileInput.addEventListener("change", handleFileSelect);

      // 拖拽事件
      uploadArea.addEventListener("dragover", handleDragOver);
      uploadArea.addEventListener("dragleave", handleDragLeave);
      uploadArea.addEventListener("drop", handleDrop);

      function handleFileSelect(event) {
        const file = event.target.files[0];
        if (file) {
          processFile(file);
        }
      }

      function handleDragOver(event) {
        event.preventDefault();
        uploadArea.classList.add("dragover");
      }

      function handleDragLeave(event) {
        event.preventDefault();
        uploadArea.classList.remove("dragover");
      }

      function handleDrop(event) {
        event.preventDefault();
        uploadArea.classList.remove("dragover");

        const files = event.dataTransfer.files;
        if (files.length > 0) {
          const file = files[0];
          if (file.type === "image/svg+xml" || file.name.toLowerCase().endsWith(".svg")) {
            processFile(file);
          } else {
            showStatus("请选择SVG文件！", "error");
          }
        }
      }

      function processFile(file) {
        if (!file.type.includes("svg") && !file.name.toLowerCase().endsWith(".svg")) {
          showStatus("请选择有效的SVG文件！", "error");
          return;
        }

        fileName = file.name.replace(".svg", "");

        const reader = new FileReader();
        reader.onload = function (e) {
          svgContent = e.target.result;
          displayPreview(svgContent);
          showStatus("SVG文件加载成功！", "success");
        };

        reader.onerror = function () {
          showStatus("文件读取失败！", "error");
        };

        reader.readAsText(file);
      }

      function displayPreview(content) {
        svgContainer.innerHTML = content;
        previewArea.classList.remove("hidden");
        convertBtn.disabled = false;
      }

      function showStatus(message, type) {
        statusDiv.textContent = message;
        statusDiv.className = `status ${type}`;
        statusDiv.classList.remove("hidden");

        // 3秒后自动隐藏状态信息
        setTimeout(() => {
          statusDiv.classList.add("hidden");
        }, 3000);
      }

      async function convertToPDF() {
        if (!svgContent) {
          showStatus("请先选择SVG文件！", "error");
          return;
        }

        try {
          convertBtn.disabled = true;
          showStatus("正在转换PDF...", "info");

          // 创建临时SVG元素来获取尺寸
          const tempDiv = document.createElement("div");
          tempDiv.innerHTML = svgContent;
          const svgElement = tempDiv.querySelector("svg");

          if (!svgElement) {
            throw new Error("无效的SVG内容");
          }

          // 获取SVG尺寸
          let width = parseFloat(svgElement.getAttribute("width")) || 800;
          let height = parseFloat(svgElement.getAttribute("height")) || 600;

          // 如果尺寸单位不是像素，设置默认值
          if (width > 2000) width = 800;
          if (height > 2000) height = 600;

          // 创建canvas来渲染SVG
          const canvas = document.createElement("canvas");
          const ctx = canvas.getContext("2d");

          // 设置canvas尺寸
          canvas.width = width;
          canvas.height = height;

          // 创建Image对象
          const img = new Image();

          // 将SVG转换为data URL
          const svgBlob = new Blob([svgContent], { type: "image/svg+xml;charset=utf-8" });
          const url = URL.createObjectURL(svgBlob);

          img.onload = function () {
            // 在canvas上绘制图像
            ctx.fillStyle = "white";
            ctx.fillRect(0, 0, width, height);
            ctx.drawImage(img, 0, 0, width, height);

            // 获取canvas数据
            const imgData = canvas.toDataURL("image/png");

            // 创建PDF
            const { jsPDF } = window.jspdf;

            // 计算PDF尺寸（A4纸张适配）
            const pdfWidth = 210; // A4宽度(mm)
            const pdfHeight = 297; // A4高度(mm)

            let imgWidth, imgHeight;
            const aspectRatio = width / height;

            if (aspectRatio > pdfWidth / pdfHeight) {
              // 图像较宽，以宽度为准
              imgWidth = pdfWidth - 20; // 留边距
              imgHeight = imgWidth / aspectRatio;
            } else {
              // 图像较高，以高度为准
              imgHeight = pdfHeight - 20; // 留边距
              imgWidth = imgHeight * aspectRatio;
            }

            const pdf = new jsPDF({
              orientation: aspectRatio > 1 ? "landscape" : "portrait",
              unit: "mm",
              format: "a4",
            });

            // 计算居中位置
            const x = (pdf.internal.pageSize.getWidth() - imgWidth) / 2;
            const y = (pdf.internal.pageSize.getHeight() - imgHeight) / 2;

            // 添加图像到PDF
            pdf.addImage(imgData, "PNG", x, y, imgWidth, imgHeight);

            // 下载PDF
            pdf.save(`${fileName}.pdf`);

            showStatus("PDF转换完成并已下载！", "success");
            convertBtn.disabled = false;

            // 清理URL
            URL.revokeObjectURL(url);
          };

          img.onerror = function () {
            throw new Error("SVG图像加载失败");
          };

          img.src = url;
        } catch (error) {
          console.error("转换错误:", error);
          showStatus(`转换失败: ${error.message}`, "error");
          convertBtn.disabled = false;
        }
      }
    </script>
  </body>
</html>
