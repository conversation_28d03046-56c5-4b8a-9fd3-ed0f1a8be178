<!doctype html>
<html>
  <body>
    <input type="file" id="fileInput" accept=".svg" />
    <button id="convertBtn" disabled>下载 PDF</button>
    <div id="svgContainer" style="display: none"></div>

    <script src="https://cdn.jsdelivr.net/npm/jspdf@latest/dist/jspdf.umd.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/svg2pdf.js@latest/dist/svg2pdf.umd.min.js"></script>
    <script>
      document.addEventListener("DOMContentLoaded", () => {
        const fileInput = document.getElementById("fileInput");
        const btn = document.getElementById("convertBtn");
        const svgContainer = document.getElementById("svgContainer");
        let rawSvgEl = null;

        fileInput.addEventListener("change", async () => {
          const file = fileInput.files?.[0];
          if (!file || !file.name.match(/\.svg$/i)) return alert("请选择 SVG");
          const text = await file.text();
          const doc = new DOMParser().parseFromString(text, "image/svg+xml");
          const svg = doc.querySelector("svg");
          if (!svg) return alert("解析不到 <svg> 节点");
          svgContainer.innerHTML = "";
          svgContainer.appendChild(svg);
          rawSvgEl = svg;
          btn.disabled = false;
        });

        btn.addEventListener("click", async () => {
          if (!rawSvgEl) return;
          try {
            const { jsPDF } = window.jspdf;
            const pdf = new jsPDF({ unit: "pt", format: "a4" /* 临时 */ });

            // 归一化 SVG
            const { clone, width, height } = normalizeSvg(rawSvgEl);

            // 重新设定 PDF 大小为 SVG 大小
            pdf.internal.pageSize.setWidth(width);
            pdf.internal.pageSize.setHeight(height);

            // 矢量绘制
            await pdf.svg(clone, { x: 0, y: 0, width, height });
            pdf.save(fileInput.files[0].name.replace(/\.svg$/i, ".pdf"));
          } catch (err) {
            console.error("svg→pdf 失败，详细信息：", err);
            alert("转换失败，控制台查看具体错误");
          }
        });

        // normalizeSvg 放这里
        function normalizeSvg(svgEl) {
          const clone = svgEl.cloneNode(true);
          if (!clone.getAttribute("xmlns")) {
            clone.setAttribute("xmlns", "http://www.w3.org/2000/svg");
          }
          let w = clone.getAttribute("width"),
            h = clone.getAttribute("height");
          if (!w || !h) {
            const vb = clone.viewBox.baseVal;
            if (vb && vb.width && vb.height) {
              w = vb.width;
              h = vb.height;
            } else {
              const bb = clone.getBBox();
              w = bb.width;
              h = bb.height;
            }
            clone.setAttribute("width", w);
            clone.setAttribute("height", h);
          }
          return { clone, width: +w, height: +h };
        }
      });
    </script>
  </body>
</html>
