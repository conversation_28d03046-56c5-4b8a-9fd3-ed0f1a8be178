<template>
  <div class="header-bg flex justify-between items-center pr-4 h-15">
    <img
      src="../../assets/images/commons/header_title.webp"
      alt=""
      srcset=""
      class="h-full object-fill"
    />
    <!-- <Menu
      v-if="['index', 'map'].includes(route.name)"
      mode="horizontal"
      :active-name="activeName"
      class="header-menu-list"
      @on-select="onMenuSelect"
    >
      <MenuItem name="2" class="header-menu-item"> 检修信息 </MenuItem>
    </Menu> -->
    <div class="flex items-center">
      <!-- <NRadioGroup v-model:value="topoStore.dataType">
        <NRadio value="开工计划" label="开工计划"></NRadio>
        <NRadio value="检修计划" label="检修计划"></NRadio>
        <NRadio value="停电平衡" label="停电平衡"></NRadio>
        <NRadio value="计划平衡" label="计划平衡"></NRadio>
        <NRadio value="光缆" label="光缆"></NRadio>
      </NRadioGroup> -->
      <div
        v-for="item in dataTypeList"
        :key="item"
        class="data-type-btn"
        :class="{ 'data-type-btn-active': topoStore.dataType === item }"
        @click="onDataTypeChange(item)"
      >
        {{ item }}
      </div>
    </div>
  </div>
  <ImportantRepair ref="importRepair"></ImportantRepair>
</template>

<script setup>
import { ref, watchEffect } from "vue";
import { useRouter, useRoute } from "vue-router";
import ImportantRepair from "@/components/ImportantRepair/index.vue";
import { useTopoStore } from "@/stores/";
import { NRadioGroup, NRadio } from "naive-ui";

const dataTypeList = ["检修计划", "开工计划", "计划平衡"];
const importRepair = ref(null);
const isDev = ref(true);
const displayName = ref("");
const activeName = ref("2");

const router = useRouter();
const route = useRoute();
const topoStore = useTopoStore();

isDev.value = import.meta.env.MODE === "development";

displayName.value = sessionStorage.getItem("displayName");

watchEffect(() => {
  if (router.currentRoute.value.path === "/index" && route.query.type === "repair") {
    activeName.value = "2";
    topoStore.setMenuSelect("2");
  } else if (router.currentRoute.value.path === "/map") {
    activeName.value = "3";
    topoStore.setMenuSelect("3");
  }
});

function onDataTypeChange(val) {
  topoStore.dataType = val;
  topoStore.repairList = [];
  topoStore.searchParams = {};
  topoStore.setTabVisible(false);
}

// function onMenuSelect(val) {
//   topoStore.setMenuSelect(val);
//   const mode = route.query.mode || "pc";
//   if (val === "1") {
//     router.push({ path: "/index", query: { mode } });
//   } else if (val === "2") {
//     router.push({ path: "/index", query: { type: "repair", mode } });
//   } else if (val === "3") {
//     router.push({ path: "/map", query: { type: "repair", mode } });
//   }
// }

// function logout() {
//   sessionStorage.clear();
//   if (import.meta.env.MODE === "development") {
//     router.push({ path: "/login" });
//   } else {
//     location.href = import.meta.env.VITE_LOGOUT_URL;
//   }
// }
</script>

<style lang="scss" scoped>
.header-bg {
  background: linear-gradient(to bottom, #2f5b89, #23456a, #1d3755, #13283e);
}

.title {
  font-size: 22px;
  font-weight: bold;
  background: #2196f3;
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  -webkit-text-fill-color: transparent;
  //   text-shadow: 0 0px 2px rgba(0, 0, 0, 0.35);

  background-image: linear-gradient(to bottom, #fefefe, #8e8e8e);
}
.data-type-btn {
  background-image: url("../../assets/images/commons/switch_btn.webp");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 139px;
  height: 40px;
  text-align: center;
  line-height: 40px;
  color: #fff;
  font-size: 18px;
  margin-left: -12px;
  opacity: 0.5;
  user-select: none;
  cursor: pointer;
}
.data-type-btn:hover {
  opacity: 0.85;
}
.data-type-btn-active {
  opacity: 1;
}
</style>
