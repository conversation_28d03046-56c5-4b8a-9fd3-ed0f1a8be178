<template>
  <n-dropdown
    placement="bottom-start"
    trigger="manual"
    :x="x"
    :y="y"
    :options="options"
    :show="showDropdown"
    :on-clickoutside="onClickoutside"
    @select="handleSelect"
  />
</template>

<script setup>
import { ref, computed } from "vue";
import { useMessage, NDropdown } from "naive-ui";

const options = [
  {
    label: "杰·盖茨比",
    key: "jay gatsby",
  },
  {
    label: "黛西·布坎南",
    key: "daisy buchanan",
  },
  {
    type: "divider",
    key: "d1",
  },
  {
    label: "尼克·卡拉威",
    key: "nick carraway",
  },
  {
    label: "其他",
    key: "others1",
    children: [
      {
        label: "乔丹·贝克",
        key: "jordan baker",
      },
      {
        label: "汤姆·布坎南",
        key: "tom buchanan",
      },
      {
        label: "其他",
        key: "others2",
        children: [
          {
            label: "鸡肉",
            key: "chicken",
          },
          {
            label: "牛肉",
            key: "beef",
          },
        ],
      },
    ],
  },
];

const message = useMessage();

const showDropdownRef = ref(false);
const xRef = ref(0);
const yRef = ref(0);

const handleContextmenu = (e) => {
  e.preventDefault();
  xRef.value = e.clientX;
  yRef.value = e.clientY;
  showDropdownRef.value = true;
};

const handleSelect = (key) => {
  message.info(`click ${key}`);
  showDropdownRef.value = false;
};

const onClickoutside = () => {
  showDropdownRef.value = false;
};

const x = computed(() => xRef.value);
const y = computed(() => yRef.value);
const showDropdown = computed(() => showDropdownRef.value);
</script>

<style lang="scss" scoped></style>
