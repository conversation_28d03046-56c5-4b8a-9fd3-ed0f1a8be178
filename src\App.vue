<template>
  <div id="app">
    <NConfigProvider
      :locale="zhCN"
      :date-locale="dateZhCN"
      :theme="darkTheme"
      :theme-overrides="themeOverrides"
    >
      <NMessageProvider>
        <BaseLayout></BaseLayout>
      </NMessageProvider>
    </NConfigProvider>
  </div>
</template>

<script setup>
import { NConfigProvider, NMessageProvider, darkTheme, zhCN, dateZhCN } from "naive-ui";
import { useRouter, useRoute } from "vue-router";
import BaseLayout from "./views/Layuot.vue";
import Cookies from "js-cookie";
import { throttle } from "lodash";

const themeOverrides = {
  common: {
    primaryColor: "#70c0e8",
    popoverColor: "#1b3651",
    primaryColorHover: "#70c0e8bf",
  },
  Button: {
    colorPrimary: "#70c0e8",
    colorHoverPrimary: "#70c0e8bf",
    colorPressedPrimary: "#70c0e8bf",
    colorFocusPrimary: "#70c0e8bf",
    textColorPrimary: "#dddddd",
    rippleColorPrimary: "#70c0e8",
    borderPrimary: "none",
    borderHoverPrimary: "none",
    borderPressedPrimary: "none",
    borderFocusPrimary: "none",
    textColorHoverPrimary: "#ddddddbf",
    textColorPressedPrimary: "#ddddddbf",
    textColorFocusPrimary: "#ddddddbf",
    textColorText: "#dddddd",
    textColorHoverText: "#ddddddbf",
    textColorPressedText: "#ddddddbf",
    textColorFocusText: "#ddddddbf",
    textColorTextHover: "#70c0e8bf",
  },
  DatePicker: {
    panelColor: "#1a2f42",
  },
  Input: {
    color: "#1a2f42",
  },
  Message: {
    iconColorSuccess: "#70c0e8",
  },
};

const router = useRouter();
const route = useRoute();
/**
 * 监测Cookie中token是否存在，不存在跳转登录页
 */
const monitorToken = () => {
  if (route.path === "/login") return;

  const checkToken = throttle(() => {
    console.log("----");

    if (!Cookies.get("token")) {
      router.replace("/login");
    }
  }, 2000); // 2秒节流时间

  // 监听鼠标移动事件
  document.addEventListener("mousemove", checkToken);
};

monitorToken();
</script>

<style>
@use "./assets/scss/common.scss";
</style>
